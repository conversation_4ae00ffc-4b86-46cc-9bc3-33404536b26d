using System;
using ScriptableEvents.Events;
using UnityEngine;

public class Triangle : MonoBehaviour, ITriangle
{
    [SerializeField] private TriangleTouch touch;
    [SerializeField] private TriangleEarth earth;
    [SerializeField] private TriangleOutline outline;
    [SerializeField] private ElementScriptableEvent elementSelected;

    private HighlightType _highlight = HighlightType.None;
    private ElementState _state = ElementState.Walkable;

    public string Name => gameObject.name;
    public HighlightType Highlight
    {
        get => _highlight;
        set
        {
            _highlight = value;
            switch (value)
            {
                case HighlightType.None:
                    outline.Clear();
                    break;
                case HighlightType.OnPath:
                    outline.SetOnPath();
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }
    }

    public ElementState State
    {
        get => _state;
        set
        {
            _state = value;
            switch (value)
            {
                case ElementState.Gathered:
                case ElementState.BlockedByPlayer:
                    earth.Regeneration = RegenerationType.None;
                    break;
                case ElementState.Regenerating:
                    earth.Regeneration = RegenerationType.None;
                    earth.Resource = ResourceTypeExtensions.GetRandomWeighted(earth.Resource);
                    break;
                case ElementState.Walkable:
                    earth.Regeneration = RegenerationType.Completed;
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }
    }

    public Vector3 Center
    {
        get
        {
            return earth.transform.position;
        }
    }

    public ResourceType Resource { get => earth.Resource; set => earth.Resource = value; }
    public RegenerationType Regeneration { get => earth.Regeneration; set => earth.Regeneration = value; }
    public bool Interactable { get => touch.Interactable; set => touch.Interactable = value; }

    public void Refresh()
    {
        State = ElementState.Walkable;
        Highlight = HighlightType.None;
        earth.PickRandomResource();
    }

    void Start()
    {
        earth.PickRandomResource();
    }

    void OnEnable()
    {
        touch.AddOnInteract(HandleInteract);
    }

    void OnDisable()
    {
        touch.RemoveOnInteract(HandleInteract);
    }

    private void HandleInteract()
    {
        elementSelected.Raise(this);
    }
}
