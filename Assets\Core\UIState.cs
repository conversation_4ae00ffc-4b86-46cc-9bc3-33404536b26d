using UnityEngine;

[RequireComponent(typeof(CanvasGroup))]
public class UIState : MonoBehaviour
{
    [SerializeField] private bool isVisible = true;
    private CanvasGroup canvasGroup;
    private bool _visible = true;
    private bool _interactable = true;

    void Awake()
    {
        canvasGroup = GetComponent<CanvasGroup>();

        Visible(isVisible);
    }

    // Update is called once per frame
    public void Visible(bool value)
    {
        _visible = value;

        canvasGroup.alpha = value ? 1 : 0;

        if (_interactable)
        {
            canvasGroup.interactable = value;
            canvasGroup.blocksRaycasts = value;
        }
    }

    public void Interactable(bool value)
    {
        _interactable = value;

        if (_visible)
        {
            canvasGroup.alpha = value ? 1 : 0.8f;
        }

        canvasGroup.interactable = value;
        canvasGroup.blocksRaycasts = value;
    }
}
