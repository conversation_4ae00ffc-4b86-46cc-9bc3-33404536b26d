using ScriptableEvents.Events;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

[RequireComponent(typeof(Button))]
public class SkillHandler : MonoBehaviour
{
    [SerializeField] private SkillId skillId;
    [SerializeField] private SkillConfiguration configuration;
    [SerializeField] private SkillIdScriptableEvent skillSelected;
    [SerializeField] private TextMeshProUGUI costUi;
    [SerializeField] private TextMeshProUGUI labelUi;
    private Button button;
    private SkillData skillData;

    void Awake()
    {
        button = GetComponent<Button>();
        button.interactable = false;
        button.onClick.AddListener(() => skillSelected.Raise(skillId)); // Tried to use button component on click, but i don't know why the Raise action is not visible in the inspector

        skillData = configuration.GetSkillData(skillId);

        costUi.text = $"Cost: {skillData.Cost}";
        labelUi.text = skillData.Label;
    }

    public void UpdateState(int energy)
    {
        if (energy < skillData.Cost)
        {
            button.interactable = false;
        }
        else
        {
            button.interactable = true;
        }
    }
}
