using System;
using UnityEngine;

[CreateAssetMenu(fileName = "New Skill Configuration", menuName = "Skills/Skill Configuration")]
public class SkillConfiguration : ScriptableObject
{
    [System.Serializable]
    public class SkillEntry
    {
        public SkillId skillId;
        public SkillData skillData;
    }

    [SerializeField] private SkillEntry[] skillEntries;

    public SkillData GetSkillData(SkillId skillId)
    {
        if (skillEntries == null) return null;

        foreach (var entry in skillEntries)
        {
            if (entry.skillId == skillId)
            {
                return entry.skillData;
            }
        }

        return null;
    }

    public int GetCost(SkillId skillId)
    {
        var skillData = GetSkillData(skillId);
        return skillData != null ? skillData.Cost : 0;
    }

    public string GetLabel(SkillId skillId)
    {
        var skillData = GetSkillData(skillId);
        return skillData != null ? skillData.Label : string.Empty;
    }
}
