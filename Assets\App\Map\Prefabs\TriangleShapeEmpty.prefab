%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &5018714711130176685
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 677440002861716169}
  - component: {fileID: 1303253129898596010}
  - component: {fileID: 3160553071728731629}
  - component: {fileID: 6643688391348806286}
  - component: {fileID: 3132890152109280305}
  m_Layer: 0
  m_Name: Shape (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &677440002861716169
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5018714711130176685}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0.7071068, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2759959813542545664}
  m_LocalEulerAnglesHint: {x: 0, y: -90, z: 0}
--- !u!114 &1303253129898596010
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5018714711130176685}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1ca002da428252441b92f28d83c8a65f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.ProBuilder::UnityEngine.ProBuilder.Shapes.ProBuilderShape
  m_Shape:
    rid: 7514059332151148785
  m_ShapeRotation: {x: 0, y: 0, z: 0, w: 1}
  m_UnmodifiedMeshVersion: 26
  m_Size: {x: -2, y: 1, z: 2}
  m_LocalCenter: {x: 0, y: 0.124091625, z: 0}
  references:
    version: 2
    RefIds:
    - rid: 7514059332151148785
      type: {class: Cylinder, ns: UnityEngine.ProBuilder.Shapes, asm: Unity.ProBuilder}
      data:
        m_AxisDivisions: 3
        m_HeightCuts: 0
        m_Smooth: 0
--- !u!23 &3160553071728731629
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5018714711130176685}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_ForceMeshLod: -1
  m_MeshLodSelectionBias: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 2
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_GlobalIlluminationMeshLod: 0
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_MaskInteraction: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &6643688391348806286
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5018714711130176685}
  m_Mesh: {fileID: 0}
--- !u!114 &3132890152109280305
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5018714711130176685}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8233d90336aea43098adf6dbabd606a2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.ProBuilder::UnityEngine.ProBuilder.ProBuilderMesh
  m_MeshFormatVersion: 2
  m_Faces:
  - m_Indexes: 000000000100000003000000010000000200000003000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: 1
  - m_Indexes: 060000000700000005000000070000000400000005000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: 1
  - m_Indexes: 0b000000080000000a0000000a0000000800000009000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: 1
  - m_Indexes: 0e0000000f0000000d0000000f0000000c0000000d000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: 1
  - m_Indexes: 100000001100000013000000110000001200000013000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: 2
  - m_Indexes: 160000001700000014000000160000001400000015000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: 2
  - m_Indexes: 18000000190000001a000000180000001a0000001b000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: 2
  - m_Indexes: 1f0000001c0000001d0000001f0000001d0000001e000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: 2
  - m_Indexes: 210000002300000020000000220000002300000021000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: 3
  - m_Indexes: 270000002400000026000000250000002600000024000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: 3
  - m_Indexes: 2a0000002800000029000000280000002a0000002b000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: 3
  - m_Indexes: 2c0000002d0000002f0000002f0000002d0000002e000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: 3
  - m_Indexes: 300000003100000032000000300000003200000033000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: 4
  - m_Indexes: 350000003600000034000000340000003600000037000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: 4
  - m_Indexes: 38000000390000003a000000380000003a0000003b000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: 4
  - m_Indexes: 3e0000003f0000003c0000003e0000003c0000003d000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: 4
  - m_Indexes: 400000004100000043000000410000004200000043000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: 5
  - m_Indexes: 470000004400000046000000440000004500000046000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: 5
  - m_Indexes: 490000004a000000480000004a0000004b00000048000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: 5
  - m_Indexes: 4f0000004c0000004d0000004f0000004d0000004e000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: 5
  - m_Indexes: 500000005100000053000000530000005100000052000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: 6
  - m_Indexes: 570000005400000056000000550000005600000054000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: 6
  - m_Indexes: 5a0000005800000059000000580000005a0000005b000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: 6
  - m_Indexes: 5d0000005f0000005c0000005e0000005f0000005d000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: 6
  - m_Indexes: 610000006300000060000000600000006300000064000000600000006400000069000000690000006400000065000000690000006500000068000000680000006500000066000000680000006600000067000000610000006200000063000000
    m_SmoothingGroup: -1
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 2100000, guid: c22777d6e868e4f2fb421913386b154e, type: 2}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: 0
    m_TextureGroup: -1
  - m_Indexes: 6c0000006b0000006a0000006c0000006d0000006b000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 700000006f0000006e00000070000000710000006f000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 740000007300000072000000740000007500000073000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 780000007700000076000000780000007900000077000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 7c0000007b0000007a0000007c0000007d0000007b000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 800000007f0000007e00000080000000810000007f000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 840000008300000082000000840000008500000083000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 880000008700000086000000880000008900000087000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 8c0000008b0000008a0000008c0000008d0000008b000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 900000008f0000008e00000090000000910000008f000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 940000009300000092000000940000009500000093000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  - m_Indexes: 980000009700000096000000980000009900000097000000
    m_SmoothingGroup: 0
    m_Uv:
      m_UseWorldSpace: 0
      m_FlipU: 0
      m_FlipV: 0
      m_SwapUV: 0
      m_Fill: 1
      m_Scale: {x: 1, y: 1}
      m_Offset: {x: 0, y: 0}
      m_Rotation: 0
      m_Anchor: 9
    m_Material: {fileID: 0}
    m_SubmeshIndex: 0
    m_ManualUV: 0
    elementGroup: -1
    m_TextureGroup: -1
  m_SharedVertices:
  - m_Vertices: 000000002900000061000000
  - m_Vertices: 01000000280000006b00000096000000
  - m_Vertices: 02000000070000009200000097000000
  - m_Vertices: 0300000006000000
  - m_Vertices: 040000000e0000008e00000093000000
  - m_Vertices: 050000000d000000
  - m_Vertices: 0b0000000f0000008a0000008f000000
  - m_Vertices: 0800000016000000860000008b000000
  - m_Vertices: 0900000015000000
  - m_Vertices: 0a0000000c000000
  - m_Vertices: 100000001e0000007e00000083000000
  - m_Vertices: 13000000170000008200000087000000
  - m_Vertices: 110000001d000000
  - m_Vertices: 1200000014000000
  - m_Vertices: 1800000026000000760000007b000000
  - m_Vertices: 1b0000001f0000007a0000007f000000
  - m_Vertices: 190000002500000067000000
  - m_Vertices: 1a0000001c000000
  - m_Vertices: 23000000270000007200000077000000
  - m_Vertices: 200000002e0000006e00000073000000
  - m_Vertices: 210000002d00000069000000
  - m_Vertices: 220000002400000068000000
  - m_Vertices: 2b0000002f0000006a0000006f000000
  - m_Vertices: 2a0000002c00000060000000
  - m_Vertices: 3000000038000000
  - m_Vertices: 3100000034000000
  - m_Vertices: 32000000370000008c00000091000000
  - m_Vertices: 33000000390000009000000095000000
  - m_Vertices: 3500000049000000
  - m_Vertices: 3600000048000000880000008d000000
  - m_Vertices: 3a0000003f0000009400000099000000
  - m_Vertices: 3b0000003e000000
  - m_Vertices: 3c000000560000006d00000098000000
  - m_Vertices: 3d0000005500000062000000
  - m_Vertices: 400000004e0000008000000085000000
  - m_Vertices: 43000000470000007c00000081000000
  - m_Vertices: 410000004d000000
  - m_Vertices: 4200000044000000
  - m_Vertices: 450000005900000066000000
  - m_Vertices: 4600000058000000
  - m_Vertices: 4b0000004f0000008400000089000000
  - m_Vertices: 4a0000004c000000
  - m_Vertices: 500000005e0000007000000075000000
  - m_Vertices: 53000000570000006c00000071000000
  - m_Vertices: 510000005d00000064000000
  - m_Vertices: 520000005400000063000000
  - m_Vertices: 5b0000005f0000007400000079000000
  - m_Vertices: 5a0000005c00000065000000
  - m_Vertices: 780000007d000000
  m_SharedTextures: []
  m_Positions:
  - {x: 1, y: 1, z: 0}
  - {x: 0.75, y: 1, z: 0}
  - {x: 0.47916666, y: 1, z: 0.18042195}
  - {x: 0.625, y: 1, z: 0.21650635}
  - {x: 0.20833331, y: 1, z: 0.3608439}
  - {x: 0.24999997, y: 1, z: 0.4330127}
  - {x: 0.625, y: 1, z: 0.21650635}
  - {x: 0.47916666, y: 1, z: 0.18042195}
  - {x: -0.37500006, y: 1, z: 0.649519}
  - {x: -0.50000006, y: 1, z: 0.8660254}
  - {x: -0.12500004, y: 1, z: 0.649519}
  - {x: -0.083333366, y: 1, z: 0.5051815}
  - {x: -0.12500004, y: 1, z: 0.649519}
  - {x: 0.24999997, y: 1, z: 0.4330127}
  - {x: 0.20833331, y: 1, z: 0.3608439}
  - {x: -0.083333366, y: 1, z: 0.5051815}
  - {x: -0.4166667, y: 1, z: -0.00000002483527}
  - {x: -0.5, y: 1, z: -0.000000029802322}
  - {x: -0.5, y: 1, z: 0.43301266}
  - {x: -0.39583337, y: 1, z: 0.3247595}
  - {x: -0.5, y: 1, z: 0.43301266}
  - {x: -0.50000006, y: 1, z: 0.8660254}
  - {x: -0.37500006, y: 1, z: 0.649519}
  - {x: -0.39583337, y: 1, z: 0.3247595}
  - {x: -0.37499994, y: 1, z: -0.6495191}
  - {x: -0.4999999, y: 1, z: -0.86602545}
  - {x: -0.49999994, y: 1, z: -0.43301272}
  - {x: -0.3958333, y: 1, z: -0.32475954}
  - {x: -0.49999994, y: 1, z: -0.43301272}
  - {x: -0.5, y: 1, z: -0.000000029802322}
  - {x: -0.4166667, y: 1, z: -0.00000002483527}
  - {x: -0.3958333, y: 1, z: -0.32475954}
  - {x: 0.20833339, y: 1, z: -0.36084396}
  - {x: 0.25000006, y: 1, z: -0.43301272}
  - {x: -0.124999925, y: 1, z: -0.6495191}
  - {x: -0.08333327, y: 1, z: -0.50518155}
  - {x: -0.124999925, y: 1, z: -0.6495191}
  - {x: -0.4999999, y: 1, z: -0.86602545}
  - {x: -0.37499994, y: 1, z: -0.6495191}
  - {x: -0.08333327, y: 1, z: -0.50518155}
  - {x: 0.75, y: 1, z: 0}
  - {x: 1, y: 1, z: 0}
  - {x: 0.625, y: 1, z: -0.21650636}
  - {x: 0.4791667, y: 1, z: -0.18042198}
  - {x: 0.625, y: 1, z: -0.21650636}
  - {x: 0.25000006, y: 1, z: -0.43301272}
  - {x: 0.20833339, y: 1, z: -0.36084396}
  - {x: 0.4791667, y: 1, z: -0.18042198}
  - {x: 0.24999997, y: 0, z: 0.4330127}
  - {x: -0.12500004, y: 0, z: 0.649519}
  - {x: -0.08333339, y: 0, z: 0.5051816}
  - {x: 0.20833337, y: 0, z: 0.360844}
  - {x: -0.12500004, y: 0, z: 0.649519}
  - {x: -0.50000006, y: 0, z: 0.8660254}
  - {x: -0.37500018, y: 0, z: 0.6495192}
  - {x: -0.08333339, y: 0, z: 0.5051816}
  - {x: 0.24999997, y: 0, z: 0.4330127}
  - {x: 0.20833337, y: 0, z: 0.360844}
  - {x: 0.47916678, y: 0, z: 0.180422}
  - {x: 0.625, y: 0, z: 0.21650635}
  - {x: 0.75000024, y: 0, z: 5.329073e-15}
  - {x: 1, y: 0, z: 0}
  - {x: 0.625, y: 0, z: 0.21650635}
  - {x: 0.47916678, y: 0, z: 0.180422}
  - {x: -0.4166668, y: 0, z: -0.000000024835277}
  - {x: -0.5, y: 0, z: -0.000000029802322}
  - {x: -0.49999994, y: 0, z: -0.43301272}
  - {x: -0.39583343, y: 0, z: -0.32475963}
  - {x: -0.49999994, y: 0, z: -0.43301272}
  - {x: -0.4999999, y: 0, z: -0.86602545}
  - {x: -0.37499994, y: 0, z: -0.6495191}
  - {x: -0.39583343, y: 0, z: -0.32475963}
  - {x: -0.37500018, y: 0, z: 0.6495192}
  - {x: -0.50000006, y: 0, z: 0.8660254}
  - {x: -0.5, y: 0, z: 0.43301266}
  - {x: -0.3958335, y: 0, z: 0.3247596}
  - {x: -0.5, y: 0, z: 0.43301266}
  - {x: -0.5, y: 0, z: -0.000000029802322}
  - {x: -0.4166668, y: 0, z: -0.000000024835277}
  - {x: -0.3958335, y: 0, z: 0.3247596}
  - {x: 0.20833345, y: 0, z: -0.36084405}
  - {x: 0.25000006, y: 0, z: -0.43301272}
  - {x: 0.625, y: 0, z: -0.21650636}
  - {x: 0.4791668, y: 0, z: -0.18042202}
  - {x: 0.625, y: 0, z: -0.21650636}
  - {x: 1, y: 0, z: 0}
  - {x: 0.75000024, y: 0, z: 5.329073e-15}
  - {x: 0.4791668, y: 0, z: -0.18042202}
  - {x: -0.37499994, y: 0, z: -0.6495191}
  - {x: -0.4999999, y: 0, z: -0.86602545}
  - {x: -0.124999925, y: 0, z: -0.6495191}
  - {x: -0.0833333, y: 0, z: -0.50518167}
  - {x: -0.124999925, y: 0, z: -0.6495191}
  - {x: 0.25000006, y: 0, z: -0.43301272}
  - {x: 0.20833345, y: 0, z: -0.36084405}
  - {x: -0.0833333, y: 0, z: -0.50518167}
  - {x: 0.625, y: 1, z: -0.21650636}
  - {x: 1, y: 1, z: 0}
  - {x: 1, y: 0, z: 0}
  - {x: 0.625, y: 0, z: -0.21650636}
  - {x: 0.25000006, y: 0, z: -0.43301272}
  - {x: -0.124999925, y: 0, z: -0.6495191}
  - {x: -0.4999999, y: 0, z: -0.86602545}
  - {x: -0.4999999, y: 1, z: -0.86602545}
  - {x: -0.124999925, y: 1, z: -0.6495191}
  - {x: 0.25000006, y: 1, z: -0.43301272}
  - {x: 0.4791667, y: 1, z: -0.18042198}
  - {x: 0.75, y: 1, z: 0}
  - {x: 0.4791668, y: 0, z: -0.18042202}
  - {x: 0.75000024, y: 0, z: 5.329073e-15}
  - {x: 0.20833339, y: 1, z: -0.36084396}
  - {x: 0.4791667, y: 1, z: -0.18042198}
  - {x: 0.20833345, y: 0, z: -0.36084405}
  - {x: 0.4791668, y: 0, z: -0.18042202}
  - {x: -0.08333327, y: 1, z: -0.50518155}
  - {x: 0.20833339, y: 1, z: -0.36084396}
  - {x: -0.0833333, y: 0, z: -0.50518167}
  - {x: 0.20833345, y: 0, z: -0.36084405}
  - {x: -0.37499994, y: 1, z: -0.6495191}
  - {x: -0.08333327, y: 1, z: -0.50518155}
  - {x: -0.37500018, y: 0, z: -0.64951944}
  - {x: -0.0833333, y: 0, z: -0.50518167}
  - {x: -0.3958333, y: 1, z: -0.32475954}
  - {x: -0.37499994, y: 1, z: -0.6495191}
  - {x: -0.39583343, y: 0, z: -0.32475963}
  - {x: -0.37500018, y: 0, z: -0.64951944}
  - {x: -0.4166667, y: 1, z: -0.00000002483527}
  - {x: -0.3958333, y: 1, z: -0.32475954}
  - {x: -0.4166668, y: 0, z: -0.000000024835277}
  - {x: -0.39583343, y: 0, z: -0.32475963}
  - {x: -0.39583337, y: 1, z: 0.3247595}
  - {x: -0.4166667, y: 1, z: -0.00000002483527}
  - {x: -0.3958335, y: 0, z: 0.3247596}
  - {x: -0.4166668, y: 0, z: -0.000000024835277}
  - {x: -0.37500006, y: 1, z: 0.649519}
  - {x: -0.39583337, y: 1, z: 0.3247595}
  - {x: -0.37500018, y: 0, z: 0.6495192}
  - {x: -0.3958335, y: 0, z: 0.3247596}
  - {x: -0.083333366, y: 1, z: 0.5051815}
  - {x: -0.37500006, y: 1, z: 0.649519}
  - {x: -0.08333339, y: 0, z: 0.5051816}
  - {x: -0.37500018, y: 0, z: 0.6495192}
  - {x: 0.20833331, y: 1, z: 0.3608439}
  - {x: -0.083333366, y: 1, z: 0.5051815}
  - {x: 0.20833337, y: 0, z: 0.360844}
  - {x: -0.08333339, y: 0, z: 0.5051816}
  - {x: 0.47916666, y: 1, z: 0.18042195}
  - {x: 0.20833331, y: 1, z: 0.3608439}
  - {x: 0.47916678, y: 0, z: 0.180422}
  - {x: 0.20833337, y: 0, z: 0.360844}
  - {x: 0.75, y: 1, z: 0}
  - {x: 0.47916666, y: 1, z: 0.18042195}
  - {x: 0.75000024, y: 0, z: 5.329073e-15}
  - {x: 0.47916678, y: 0, z: 0.180422}
  m_Textures0:
  - {x: 1, y: 0}
  - {x: 0.75, y: 0}
  - {x: 0.47916666, y: 0.18042195}
  - {x: 0.625, y: 0.21650635}
  - {x: 0.20833331, y: 0.3608439}
  - {x: 0.24999997, y: 0.4330127}
  - {x: 0.625, y: 0.21650635}
  - {x: 0.47916666, y: 0.18042195}
  - {x: -0.37500006, y: 0.649519}
  - {x: -0.50000006, y: 0.8660254}
  - {x: -0.12500004, y: 0.649519}
  - {x: -0.083333366, y: 0.5051815}
  - {x: -0.12500004, y: 0.649519}
  - {x: 0.24999997, y: 0.4330127}
  - {x: 0.20833331, y: 0.3608439}
  - {x: -0.083333366, y: 0.5051815}
  - {x: -0.4166667, y: -0.00000002483527}
  - {x: -0.5, y: -0.000000029802322}
  - {x: -0.5, y: 0.43301266}
  - {x: -0.39583337, y: 0.3247595}
  - {x: -0.5, y: 0.43301266}
  - {x: -0.50000006, y: 0.8660254}
  - {x: -0.37500006, y: 0.649519}
  - {x: -0.39583337, y: 0.3247595}
  - {x: -0.37499994, y: -0.6495191}
  - {x: -0.4999999, y: -0.86602545}
  - {x: -0.49999994, y: -0.43301272}
  - {x: -0.3958333, y: -0.32475954}
  - {x: -0.49999994, y: -0.43301272}
  - {x: -0.5, y: -0.000000029802322}
  - {x: -0.4166667, y: -0.00000002483527}
  - {x: -0.3958333, y: -0.32475954}
  - {x: 0.20833339, y: -0.36084396}
  - {x: 0.25000006, y: -0.43301272}
  - {x: -0.124999925, y: -0.6495191}
  - {x: -0.08333327, y: -0.50518155}
  - {x: -0.124999925, y: -0.6495191}
  - {x: -0.4999999, y: -0.86602545}
  - {x: -0.37499994, y: -0.6495191}
  - {x: -0.08333327, y: -0.50518155}
  - {x: 0.75, y: 0}
  - {x: 1, y: 0}
  - {x: 0.625, y: -0.21650636}
  - {x: 0.4791667, y: -0.18042198}
  - {x: 0.625, y: -0.21650636}
  - {x: 0.25000006, y: -0.43301272}
  - {x: 0.20833339, y: -0.36084396}
  - {x: 0.4791667, y: -0.18042198}
  - {x: -0.24999997, y: 0.4330127}
  - {x: 0.12500004, y: 0.649519}
  - {x: 0.08333339, y: 0.5051816}
  - {x: -0.20833337, y: 0.360844}
  - {x: 0.12500004, y: 0.649519}
  - {x: 0.50000006, y: 0.8660254}
  - {x: 0.37500018, y: 0.6495192}
  - {x: 0.08333339, y: 0.5051816}
  - {x: -0.24999997, y: 0.4330127}
  - {x: -0.20833337, y: 0.360844}
  - {x: -0.47916678, y: 0.180422}
  - {x: -0.625, y: 0.21650635}
  - {x: -0.75000024, y: 5.329073e-15}
  - {x: -1, y: 0}
  - {x: -0.625, y: 0.21650635}
  - {x: -0.47916678, y: 0.180422}
  - {x: 0.4166668, y: -0.000000024835277}
  - {x: 0.5, y: -0.000000029802322}
  - {x: 0.49999994, y: -0.43301272}
  - {x: 0.39583343, y: -0.32475963}
  - {x: 0.49999994, y: -0.43301272}
  - {x: 0.4999999, y: -0.86602545}
  - {x: 0.37499994, y: -0.6495191}
  - {x: 0.39583343, y: -0.32475963}
  - {x: 0.37500018, y: 0.6495192}
  - {x: 0.50000006, y: 0.8660254}
  - {x: 0.5, y: 0.43301266}
  - {x: 0.3958335, y: 0.3247596}
  - {x: 0.5, y: 0.43301266}
  - {x: 0.5, y: -0.000000029802322}
  - {x: 0.4166668, y: -0.000000024835277}
  - {x: 0.3958335, y: 0.3247596}
  - {x: -0.20833345, y: -0.36084405}
  - {x: -0.25000006, y: -0.43301272}
  - {x: -0.625, y: -0.21650636}
  - {x: -0.4791668, y: -0.18042202}
  - {x: -0.625, y: -0.21650636}
  - {x: -1, y: 0}
  - {x: -0.75000024, y: 5.329073e-15}
  - {x: -0.4791668, y: -0.18042202}
  - {x: 0.37499994, y: -0.6495191}
  - {x: 0.4999999, y: -0.86602545}
  - {x: 0.124999925, y: -0.6495191}
  - {x: 0.0833333, y: -0.50518167}
  - {x: 0.124999925, y: -0.6495191}
  - {x: -0.25000006, y: -0.43301272}
  - {x: -0.20833345, y: -0.36084405}
  - {x: 0.0833333, y: -0.50518167}
  - {x: 0.43301266, y: 1}
  - {x: 0.8660254, y: 1}
  - {x: 0.8660254, y: 0.0000000059604663}
  - {x: 0.43301266, y: 0.000000005960466}
  - {x: -0.000000014901165, y: 0.0000000059604663}
  - {x: -0.43301272, y: 0.000000005960466}
  - {x: -0.86602545, y: 0.000000005960466}
  - {x: -0.86602545, y: 1}
  - {x: -0.43301272, y: 1}
  - {x: -0.000000014901165, y: 1}
  - {x: -0.2987527, y: 0.99999994}
  - {x: -0.6241798, y: 0.99999994}
  - {x: -0.29875275, y: -0.00000004295154}
  - {x: -0.62417996, y: -0.000000042951545}
  - {x: 0.026674371, y: 0.99999994}
  - {x: -0.2987527, y: 0.99999994}
  - {x: 0.026674371, y: -0.00000004468059}
  - {x: -0.29875275, y: -0.00000004468059}
  - {x: 0.29875273, y: 0.99999994}
  - {x: -0.02667435, y: 0.99999994}
  - {x: 0.29875278, y: -0.00000003892999}
  - {x: -0.026674364, y: -0.00000003892999}
  - {x: 0.6241797, y: 0.99999994}
  - {x: 0.29875267, y: 0.99999994}
  - {x: 0.6241801, y: -0.00000008930838}
  - {x: 0.29875273, y: -0.00000008930835}
  - {x: -0.2987527, y: 0.99999994}
  - {x: -0.6241798, y: 0.99999994}
  - {x: -0.29875275, y: -0.00000005184698}
  - {x: -0.62418014, y: -0.000000051846996}
  - {x: 0.026674377, y: 0.99999994}
  - {x: -0.2987527, y: 0.99999994}
  - {x: 0.026674384, y: -0.000000049466987}
  - {x: -0.29875275, y: -0.000000049466987}
  - {x: 0.29875273, y: 0.99999994}
  - {x: -0.026674349, y: 0.99999994}
  - {x: 0.2987528, y: -0.00000005184697}
  - {x: -0.026674356, y: -0.00000005184697}
  - {x: 0.6241798, y: 0.99999994}
  - {x: 0.29875273, y: 0.99999994}
  - {x: 0.62417996, y: -0.00000005422694}
  - {x: 0.2987528, y: -0.00000005422694}
  - {x: -0.2987527, y: 0.99999994}
  - {x: -0.6241798, y: 0.99999994}
  - {x: -0.29875275, y: -0.000000040304077}
  - {x: -0.62417996, y: -0.00000004030408}
  - {x: 0.026674323, y: 0.99999994}
  - {x: -0.29875278, y: 0.99999994}
  - {x: 0.026674336, y: -0.000000044312397}
  - {x: -0.29875284, y: -0.000000044312397}
  - {x: 0.29875273, y: 0.99999994}
  - {x: -0.026674345, y: 0.99999994}
  - {x: 0.2987528, y: -0.000000042951534}
  - {x: -0.026674345, y: -0.000000042951534}
  - {x: 0.62417984, y: 0.99999994}
  - {x: 0.29875273, y: 0.99999994}
  - {x: 0.62418, y: -0.000000054963316}
  - {x: 0.2987528, y: -0.00000005496331}
  m_Textures2:
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  m_Textures3:
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  - {x: 0, y: 0, z: 0, w: 0}
  m_Tangents:
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: 1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: -1, y: 0, z: 0, w: -1}
  - {x: 0.8660254, y: 0, z: 0.5000001, w: -1}
  - {x: 0.8660254, y: 0, z: 0.5000001, w: -1}
  - {x: 0.8660254, y: 0, z: 0.5000001, w: -1}
  - {x: 0.8660254, y: 0, z: 0.5000001, w: -1}
  - {x: 0.8660254, y: 0, z: 0.5000001, w: -1}
  - {x: 0.8660254, y: 0, z: 0.5000001, w: -1}
  - {x: 0.8660254, y: 0, z: 0.5000001, w: -1}
  - {x: 0.8660254, y: 0, z: 0.5000001, w: -1}
  - {x: 0.8660254, y: 0, z: 0.5000001, w: -1}
  - {x: 0.8660254, y: 0, z: 0.5000001, w: -1}
  - {x: -0.8322397, y: 0, z: -0.55441606, w: -1}
  - {x: -0.8322397, y: -8.967443e-15, z: -0.554416, w: -1}
  - {x: -0.8322397, y: -8.967443e-15, z: -0.554416, w: -1}
  - {x: -0.8322398, y: -1.8795802e-14, z: -0.55441594, w: -1}
  - {x: -0.8322397, y: 0, z: -0.55441606, w: -1}
  - {x: -0.8322397, y: 0, z: -0.55441606, w: -1}
  - {x: -0.8322397, y: 0, z: -0.55441606, w: -1}
  - {x: -0.8322397, y: -3.0784476e-15, z: -0.5544161, w: -1}
  - {x: -0.8962581, y: 0, z: -0.44353282, w: -1}
  - {x: -0.8962581, y: 0, z: -0.44353282, w: -1}
  - {x: -0.8962581, y: 0, z: -0.44353282, w: -1}
  - {x: -0.8962582, y: 0, z: -0.44353282, w: -1}
  - {x: -0.89625823, y: 6.400958e-15, z: -0.44353268, w: -1}
  - {x: -0.8962581, y: 4.8066904e-14, z: -0.44353285, w: -1}
  - {x: -0.8962581, y: 4.8066904e-14, z: -0.44353285, w: -1}
  - {x: -0.89625806, y: 8.733658e-14, z: -0.44353303, w: -1}
  - {x: -0.06401857, y: 9.290015e-16, z: 0.99794877, w: -1}
  - {x: -0.064018354, y: -2.7292675e-14, z: 0.9979487, w: -1}
  - {x: -0.064018354, y: -2.7292675e-14, z: 0.9979487, w: -1}
  - {x: -0.06401815, y: -5.4585358e-14, z: 0.99794877, w: -1}
  - {x: -0.06401856, y: -8.863565e-16, z: 0.9979487, w: -1}
  - {x: -0.064018555, y: -9.076789e-16, z: 0.9979487, w: -1}
  - {x: -0.064018555, y: -9.076789e-16, z: 0.9979487, w: -1}
  - {x: -0.06401855, y: 0, z: 0.9979487, w: -1}
  - {x: 0.06401838, y: -9.290014e-16, z: 0.99794877, w: -1}
  - {x: 0.064018376, y: -9.076789e-16, z: 0.99794877, w: -1}
  - {x: 0.064018376, y: -9.076789e-16, z: 0.99794877, w: -1}
  - {x: 0.06401836, y: -8.863565e-16, z: 0.9979487, w: -1}
  - {x: 0.06401838, y: 0, z: 0.9979487, w: -1}
  - {x: 0.06401837, y: 0, z: 0.9979487, w: -1}
  - {x: 0.06401837, y: 0, z: 0.9979487, w: -1}
  - {x: 0.06401836, y: 0, z: 0.9979487, w: -1}
  - {x: 0.89625823, y: 0, z: -0.44353262, w: -1}
  - {x: 0.89625823, y: -5.4585382e-15, z: -0.44353265, w: -1}
  - {x: 0.89625823, y: -5.4585382e-15, z: -0.44353265, w: -1}
  - {x: 0.89625823, y: -1.09170765e-14, z: -0.44353268, w: -1}
  - {x: 0.8962581, y: 0, z: -0.4435328, w: -1}
  - {x: 0.8962581, y: -3.1800664e-15, z: -0.44353282, w: -1}
  - {x: 0.8962581, y: -3.1800664e-15, z: -0.44353282, w: -1}
  - {x: 0.8962581, y: 0, z: -0.4435328, w: -1}
  - {x: 0.83223975, y: -3.0784472e-15, z: -0.5544159, w: -1}
  - {x: 0.83223975, y: 0, z: -0.55441594, w: -1}
  - {x: 0.83223975, y: 0, z: -0.55441594, w: -1}
  - {x: 0.83223975, y: 0, z: -0.55441594, w: -1}
  - {x: 0.83223975, y: 0, z: -0.55441594, w: -1}
  - {x: 0.83223975, y: 3.899268e-15, z: -0.55441594, w: -1}
  - {x: 0.83223975, y: 3.899268e-15, z: -0.55441594, w: -1}
  - {x: 0.8322398, y: 2.1834155e-14, z: -0.5544159, w: -1}
  m_Colors: []
  m_UnwrapParameters:
    m_HardAngle: 88
    m_PackMargin: 20
    m_AngleError: 8
    m_AreaError: 15
  m_PreserveMeshAssetOnDestroy: 0
  assetGuid: 
  m_Mesh: {fileID: 0}
  m_VersionIndex: 150
  m_IsSelectable: 1
  m_SelectedFaces: 
  m_SelectedEdges: []
  m_SelectedVertices: 
--- !u!1 &6304128463757503787
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2759959813542545664}
  m_Layer: 0
  m_Name: TriangleShapeEmpty
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2759959813542545664
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6304128463757503787}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 0.1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 677440002861716169}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
