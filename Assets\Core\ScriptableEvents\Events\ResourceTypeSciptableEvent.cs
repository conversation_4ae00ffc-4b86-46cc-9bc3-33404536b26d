using CHARK.ScriptableEvents;
using UnityEngine;

namespace ScriptableEvents.Events
{
    [CreateAssetMenu(
        fileName = "ResourceTypeScriptableEvent",
        menuName = ScriptableEventConstants.MenuNameCustom + "/Resource Type Scriptable Event",
        order = ScriptableEventConstants.MenuOrderCustom + 0
    )]
    internal sealed class ResourceTypeScriptableEvent : ScriptableEvent<ResourceType>
    {
    }
}
