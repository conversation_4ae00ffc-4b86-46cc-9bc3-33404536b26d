using System.Collections.Generic;
using CHARK.ScriptableEvents.Events;
using UnityEngine;
using DG.Tweening;
using ScriptableEvents.Events;
using UnityEngine.Serialization;
using System;

public class PathController : MonoBehaviour
{
    [SerializeField] private MapManager mapManager;
    [SerializeField] private ElementScriptableEvent elementSelected;
    [SerializeField] private ResourceTypeScriptableEvent resourceGathered;
    [SerializeField] private IntScriptableEvent amountGathered;
    [SerializeField] private StringScriptableEvent endGame;
    [FormerlySerializedAs("pathMarker")]
    [SerializeField] private GameObject marker;

    private readonly List<ITriangle> path = new();
    private ITriangle startLocation;
    private ITriangle markerLocation;

    public bool HasPath => path.Count > 0;

    public void EnableBuildMode(ITriangle start)
    {
        startLocation = start;
        markerLocation = start;

        MoveMarkerTo(start);

        elementSelected.AddListener(OnElementSelected);

        int validNeighbors = ActivateNeighbors(markerLocation, false);

        if (validNeighbors == 0)
        {
            endGame.Raise("player blocked");
            DisableBuildMode();
        }
    }

    public void DisableBuildMode()
    {
        elementSelected.RemoveListener(OnElementSelected);

        DeactivateNeighbors(markerLocation);

        Clear();
        MoveMarkerTo(startLocation);
    }

    private void Clear()
    {
        path.ForEach(p => p.Highlight = HighlightType.None);
        path.Clear();
    }

    private void MoveMarkerTo(ITriangle elem)
    {
        marker.transform.position = new Vector3(elem.Center.x, marker.transform.position.y, elem.Center.z);
    }

    private void OnElementSelected(ITriangle elem)
    {
        Debug.Log("Selected element! " + elem);

        DeactivateNeighbors(markerLocation);

        elem.Highlight = HighlightType.OnPath;

        path.Add(elem);
        marker.transform.position = new Vector3(elem.Center.x, marker.transform.position.y, elem.Center.z);

        markerLocation = elem;

        ActivateNeighbors(markerLocation);
    }

    /**
     * Will also handle like {DisableBuildMode} when called
     */
    public void WalkOnPath(GameObject player, Action callback)
    {
        Debug.Log("Walking on path: " + path);

        elementSelected.RemoveListener(OnElementSelected);
        DeactivateNeighbors(markerLocation);

        int elementsGathered = 0;

        startLocation.State = ElementState.Gathered; // The start location is already gathered. We don't count it but it should start Regenerating.

        void step(int index)
        {
            ITriangle current = path[index];

            Vector3 targetPosition = new(
                current.Center.x,
                player.transform.position.y,
                current.Center.z
            );

            player.transform.DOMove(targetPosition, 0.3f).OnComplete(() =>
            {
                current.Highlight = HighlightType.None; // Remove the highlight
                elementsGathered++;
                resourceGathered.Raise(current.Resource);

                if (index == path.Count - 1)
                {
                    current.State = ElementState.BlockedByPlayer;
                    onComplete();
                }
                else
                {
                    current.State = ElementState.Gathered;
                    step(index + 1); // Move to the next GameObject
                }
            });
        }

        void onComplete()
        {
            amountGathered.Raise(elementsGathered);
            path.Clear();
            callback.Invoke();
        }

        step(0);
    }

    private int ActivateNeighbors(ITriangle tri, bool withSameResource = true)
    {
        MapElement elem = mapManager.FindByName(tri.Name);
        List<MapElement> findValidNeighbors = mapManager
            .GetNeighbors(elem)
            .FindAll(n =>
                {
                    if (n == startLocation) return false;
                    if (path.Contains(n.GameObject)) return false;

                    if (withSameResource && n.GameObject.Resource == tri.Resource && n.GameObject.State == ElementState.Walkable)
                    {
                        return true;
                    }

                    if (!withSameResource && n.GameObject.State == ElementState.Walkable)
                    {
                        return true;
                    }

                    return false;
                }
            );

        findValidNeighbors.ForEach(n =>
        {
            n.GameObject.Interactable = true;
        });

        return findValidNeighbors.Count;
    }

    private void DeactivateNeighbors(ITriangle tri)
    {
        MapElement elem = mapManager.FindByName(tri.Name);
        mapManager.GetNeighbors(elem)
            .FindAll(n =>
                {
                    if (n == startLocation) return false;
                    if (path.Contains(n.GameObject)) return false;

                    return true;
                }
            )
            .ForEach(n =>
            {
                n.GameObject.Interactable = false;
            });
    }
}
