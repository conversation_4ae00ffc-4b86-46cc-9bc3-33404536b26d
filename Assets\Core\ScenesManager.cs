using UnityEngine;
using UnityEngine.SceneManagement;

public class ScenesManager : MonoBehaviour
{
    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        // Only load if not already loaded
        if (!SceneManager.GetSceneByName("Map").isLoaded)
        {
            SceneManager.LoadScene("Map", LoadSceneMode.Additive);
        }
    }
}
