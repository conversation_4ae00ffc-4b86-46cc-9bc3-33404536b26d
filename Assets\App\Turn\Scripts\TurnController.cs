using CHARK.ScriptableEvents.Events;
using TMPro;
using UnityEngine;

public class TurnController : MonoBehaviour
{
    [SerializeField] private int totalTurns;
    [SerializeField] private TextMeshProUGUI textUi;
    [SerializeField] private StringScriptableEvent endGame;

    private int currentTurnNo = 1;
    // Start is called once before the first execution of Update after the MonoBehaviour is created

    public void Restart()
    {
        currentTurnNo = 1;
        UpdateTurn();
    }

    public void NextTurn()
    {
        if (currentTurnNo == totalTurns)
        {
            endGame.Raise("turns over");
            return;
        }

        currentTurnNo++;
        UpdateTurn();
    }

    void UpdateTurn()
    {
        textUi.text = $"Turn<br>{currentTurnNo}/{totalTurns}";
    }
}
