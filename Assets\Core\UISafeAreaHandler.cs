using UnityEngine;

public class UISafeAreaHandler : MonoBehaviour
{
    RectTransform rectTransform;
    Rect lastSafeArea = new Rect(0, 0, 0, 0);

    void Awake() => rectTransform = GetComponent<RectTransform>();

    void Update()
    {
        if (lastSafeArea != Screen.safeArea) ApplySafeArea(Screen.safeArea);
    }

    void ApplySafeArea(Rect area)
    {
        lastSafeArea = area;

        // Convert safe area rectangle from pixel space to anchor space
        Vector2 anchorMin = area.position;
        Vector2 anchorMax = area.position + area.size;

        anchorMin.x /= Screen.width;
        anchorMin.y /= Screen.height;
        anchorMax.x /= Screen.width;
        anchorMax.y /= Screen.height;

        rectTransform.anchorMin = anchorMin;
        rectTransform.anchorMax = anchorMax;
    }
}