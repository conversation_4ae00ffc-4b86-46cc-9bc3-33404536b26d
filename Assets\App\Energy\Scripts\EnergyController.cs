using System;
using CHARK.ScriptableEvents.Events;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class EnergyController : MonoBehaviour
{
    [SerializeField] private TextMeshProUGUI textUi;
    [SerializeField] private Slider energyBar;
    [SerializeField] private IntScriptableEvent energyUpdated;
    [SerializeField] private StringScriptableEvent endGame;
    [SerializeField] private int startEnergyAmount;
    public int Energy { get; private set; } = 0;

    public void Reset()
    {
        Energy = startEnergyAmount;
        UpdateEnergy();
    }

    public void AddEnergy(int amount)
    {
        Energy += CalculateAmountEarned(amount);
        Energy = Math.Min(Energy, 100);

        if (Energy <= 0)
        {
            Energy = 0;
            endGame.Raise("energy over");
        }

        UpdateEnergy();
    }

    private static int CalculateAmountEarned(int value)
    {
        /* int score = value switch
        {
            1 => -5,
            2 => 0,
            3 => 3,
            4 => 4,
            _ => (int)Math.Ceiling(3 + (value - 3) * Math.Pow(value, value * 0.05))
        }; */

        int score = value switch
        {
            1 => 20,
            2 => 18,
            3 => 15,
            4 => 11,
            5 => 7,
            6 => 1,
            7 => -2,
            _ => -5,
        };

        return score;
    }

    // Update is called once per frame
    void UpdateEnergy()
    {
        textUi.text = $"{Energy}";
        energyBar.value = Energy;
        energyUpdated.Raise(Energy);
    }
}
