using CHARK.ScriptableEvents.Events;
using UnityEngine;

public class PlayerController : MonoBehaviour
{
    [SerializeField] private PathController pathController;
    [SerializeField] private SkillsController skillsController;
    [SerializeField] private GameObject player;
    [SerializeField] private SimpleScriptableEvent turnInProgress;
    [SerializeField] private SimpleScriptableEvent turnCompleted;

    private ITriangle elementUnderPlayer = null;

    public void Restart(ITriangle elem)
    {
        elementUnderPlayer = elem;
        elementUnderPlayer.State = ElementState.BlockedByPlayer;
        player.transform.position = new Vector3(elementUnderPlayer.Center.x, player.transform.position.y, elementUnderPlayer.Center.z);

        pathController.EnableBuildMode(elementUnderPlayer);
    }

    public void RegisterSkill(SkillId skillId)
    {
        pathController.DisableBuildMode();
        skillsController.EnableUseMode(skillId);
    }

    public void UnregisterSkill()
    {
        skillsController.DisableUseMode();
        pathController.EnableBuildMode(elementUnderPlayer);
    }

    public void EndTurn()
    {
        if (!skillsController.HasSkill && !pathController.HasPath)
        {
            Debug.Log("No action performed! Can't end turn!");

            return;
        }

        if (skillsController.HasSkill)
        {
            turnInProgress.Raise();
            skillsController.ExecuteSkill(() =>
            {
                turnCompleted.Raise();
            });

            return;
        }

        if (pathController.HasPath)
        {
            turnInProgress.Raise();
            pathController.WalkOnPath(player, () =>
            {
                turnCompleted.Raise();
            });

            return;
        }
    }
}