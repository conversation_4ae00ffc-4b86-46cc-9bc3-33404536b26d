using System;
using UnityEngine;

public class SkillsController : MonoBehaviour
{
    [SerializeField] private SkillConfiguration skillConfiguration;
    private SkillId? currentSkill;

    public bool HasSkill => currentSkill.HasValue;

    public void EnableUseMode(SkillId id)
    {
        currentSkill = id;
    }

    public void DisableUseMode()
    {
        currentSkill = null;
    }

    public void ExecuteSkill(Action callback)
    {
        Debug.Log("Executing skill: " + currentSkill);

        callback.Invoke();
    }
}